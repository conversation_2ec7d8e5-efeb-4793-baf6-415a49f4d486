const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const jwt = require('jsonwebtoken');

const app = express();
const PORT = 3001;

app.use(cors());
app.use(bodyParser.json());

app.post('/api/generate-token', (req, res) => {
  try {
    const { email } = req.body;
    
    const METABASE_SECRET_KEY = "376a52aa37037da98a9b593be727b3f73d712bec37a76882d0204b22ff9c7265";
    
    const payload = {
      resource: { dashboard: 122 },
      params: {
        "email_snd": [email || ""]
      },
      exp: Math.round(Date.now() / 1000) + (10 * 60) // 10 minute expiration
    };

    const token = jwt.sign(payload, METABASE_SECRET_KEY);
    res.json({ token });
  } catch (error) {
    console.error('Error generating token:', error);
    res.status(500).json({ error: 'Failed to generate token' });
  }
});

app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
});
