import logo from './logo.svg';
import './App.css';
// import jwt from 'jsonwebtoken';

function App() {
const jwt = require("jsonwebtoken");

const METABASE_SITE_URL = "http://metabase.happyfresh.net";
const METABASE_SECRET_KEY = "376a52aa37037da98a9b593be727b3f73d712bec37a76882d0204b22ff9c7265";

const payload = {
  resource: { dashboard: 122 },
  params: {
    "email_snd": [
      "<EMAIL>"
    ]
  },
  exp: Math.round(Date.now() / 1000) + (10 * 60) // 10 minute expiration
};
const token = jwt.sign(payload, METABASE_SECRET_KEY);

const iframeUrl = METABASE_SITE_URL + "/embed/dashboard/" + token +
  "#bordered=true&titled=true";

  return (
    <div className="App">
      <iframe
      src={iframeUrl}
      width={800}
      height={600}
      allowTransparency
      />
    </div>
  );
}


export default App;
